#!/usr/bin/env python3
"""
直接使用ChromaDB进行memory压缩测试
"""

import os
import sys
import time
import chromadb
from chromadb.config import Settings

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from config import get_config
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.memory import MemoryContent, MemoryMimeType
from autogen_core.models import SystemMessage, UserMessage

async def compress_memories_directly():
    """直接使用ChromaDB进行memory压缩"""
    print("开始直接压缩用户chieh.chu的记忆...")

    # 获取配置
    config = get_config()

    # 创建LLM客户端
    llm_client = OpenAIChatCompletionClient(
        model=config['agents']['memory_system']['agent_model_name'],
        api_key=config['agents']['memory_system']['agent_model_api_key'],
        model_info={
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": "unknown",
            "structured_output": True,
        }
    )

    # 连接到用户的ChromaDB
    user_id = "chieh.chu"
    user_path = os.path.join(os.path.dirname(__file__), "..", "data", "memories", user_id)

    if not os.path.exists(user_path):
        print(f"用户记忆路径不存在: {user_path}")
        return

    print(f"连接到用户记忆数据库: {user_path}")

    try:
        client = chromadb.PersistentClient(
            path=user_path,
            settings=Settings(anonymized_telemetry=False)
        )

        # 获取短期记忆集合
        short_term_collection = client.get_collection("memory_short_term")
        medium_term_collection = client.get_collection("memory_medium_term")

        # 获取所有短期记忆
        short_memories = short_term_collection.get(include=['documents', 'metadatas'])

        print(f"找到 {len(short_memories['ids'])} 个短期记忆")

        if len(short_memories['ids']) == 0:
            print("没有短期记忆需要压缩")
            return

        # 获取配置的阈值
        count_threshold = config['agents']['memory_system']['compression_thresholds']['short_to_medium_count']
        print(f"压缩阈值: {count_threshold}")

        if len(short_memories['ids']) >= count_threshold:
            print(f"短期记忆数量 ({len(short_memories['ids'])}) 超过阈值 ({count_threshold})，开始压缩...")

            # 按时间排序，选择最老的记忆进行压缩
            memories_with_time = []
            for i, (doc_id, metadata, document) in enumerate(zip(short_memories['ids'], short_memories['metadatas'], short_memories['documents'])):
                created_at = metadata.get('created_at', time.time())
                memories_with_time.append((created_at, doc_id, metadata, document))

            # 按时间排序（最老的在前）
            memories_with_time.sort(key=lambda x: x[0])

            # 选择要压缩的记忆数量
            excess_count = len(short_memories['ids']) - count_threshold + 10
            memories_to_compress = memories_with_time[:excess_count]

            print(f"选择 {len(memories_to_compress)} 个最老的记忆进行压缩")

            # 压缩记忆
            for created_at, doc_id, metadata, document in memories_to_compress:
                print(f"压缩记忆: {doc_id[:8]}... (创建时间: {created_at})")

                # 使用LLM压缩记忆内容
                compression_prompt = f"""
                请将以下短期记忆内容压缩为更简洁的中期记忆表示。
                保留关键信息、事实和见解，同时删除冗余细节。
                这些信息需要在几天到几周内保持相关性。

                短期记忆内容:
                {document}

                请输出压缩后的中期记忆:
                """

                try:
                    # 调用LLM进行压缩
                    messages = [
                        SystemMessage(content="你是一个有用的助手，负责压缩记忆以进行长期存储。"),
                        UserMessage(content=compression_prompt, source="user")
                    ]

                    response = await llm_client.create(messages)
                    compressed_content = response.content.strip()

                    print(f"  原始内容: {document[:100]}...")
                    print(f"  压缩内容: {compressed_content[:100]}...")

                    # 更新元数据
                    new_metadata = metadata.copy()
                    new_metadata['memory_level'] = 'medium_term'
                    new_metadata['compressed_at'] = time.time()
                    new_metadata['original_memory_id'] = doc_id

                    # 添加到中期记忆集合
                    medium_term_collection.add(
                        documents=[compressed_content],
                        metadatas=[new_metadata],
                        ids=[f"compressed_{doc_id}"]
                    )

                    # 从短期记忆中删除
                    short_term_collection.delete(ids=[doc_id])

                    print(f"  成功压缩并移动记忆: {doc_id}")

                except Exception as e:
                    print(f"  压缩记忆失败: {e}")
                    continue

            print("记忆压缩完成！")

            # 查询压缩后的状态
            short_memories_after = short_term_collection.get(include=['documents', 'metadatas'])
            medium_memories_after = medium_term_collection.get(include=['documents', 'metadatas'])

            print(f"压缩后 - 短期记忆: {len(short_memories_after['ids'])}, 中期记忆: {len(medium_memories_after['ids'])}")

        else:
            print(f"短期记忆数量 ({len(short_memories['ids'])}) 未超过阈值 ({count_threshold})，无需压缩")

    except Exception as e:
        print(f"压缩过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import asyncio
    asyncio.run(compress_memories_directly())
