#!/usr/bin/env python3
"""
测试修复后的memory压缩功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from utils.memory.multi_user_memory import MultiUserMemory, MultiUserMemoryConfig
from autogen_core.memory import MemoryContent, MemoryMimeType
from autogen_ext.models.openai import OpenAIChatCompletionClient
from config import get_config

async def test_fixed_compression():
    """测试修复后的memory压缩功能"""
    print("开始测试修复后的memory压缩功能...")
    
    # 获取配置
    config = get_config()
    
    # 创建LLM客户端
    llm_client = OpenAIChatCompletionClient(
        model=config['agents']['memory_system']['agent_model_name'],
        api_key=config['agents']['memory_system']['agent_model_api_key'],
        model_info={
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": "unknown",
            "structured_output": True,
        }
    )
    
    # 创建记忆系统配置
    memory_config = MultiUserMemoryConfig(
        base_path=os.path.join(os.path.dirname(__file__), "..", "data", "memories"),
        shared_memory_enabled=False,
        user_isolation="db_path",
        collection_name="test_fixed_compression",
        
        # 记忆压缩设置
        enable_memory_compression=True,
        llm_client=llm_client,
        
        # 设置较小的阈值以便测试
        short_to_medium_threshold=60,  # 1分钟
        medium_to_long_threshold=120,  # 2分钟
        short_to_medium_count_threshold=3,  # 3条消息就触发压缩
        medium_to_long_count_threshold=5,  # 5条消息就触发压缩
        
        # 查询设置
        short_term_k=10,
        medium_term_k=5,
        long_term_k=3,
        
        # 维护设置
        maintenance_interval=1  # 每次操作后执行维护
    )
    
    # 创建记忆系统
    memory = MultiUserMemory(memory_config)
    
    try:
        user_id = "test_fixed_user"
        
        print(f"为用户 {user_id} 添加测试记忆...")
        
        # 添加多条记忆以触发基于数量的压缩
        test_memories = [
            "学生问了关于二次方程的问题，解释了求根公式的推导过程。",
            "学生在解一元二次方程时出现了计算错误，需要加强基础运算。",
            "学生掌握了配方法，但对判别式的理解还不够深入。",
            "学生问了关于函数图像的问题，解释了抛物线的开口方向。",
            "学生在做函数题时，对定义域和值域的概念混淆。"
        ]
        
        for i, content in enumerate(test_memories):
            memory_content = MemoryContent(
                content=content,
                mime_type=MemoryMimeType.TEXT,
                metadata={
                    "test_id": i+1,
                    "importance": 0.7,
                    "topic": "数学学习"
                }
            )
            await memory.add(memory_content, user_id)
            print(f"添加了第{i+1}条记忆: {content[:30]}...")
        
        print("\n查询压缩后的记忆...")
        
        # 查询各层级记忆
        short_results = await memory.query("数学", user_id, memory_level="short_term")
        medium_results = await memory.query("数学", user_id, memory_level="medium_term")
        long_results = await memory.query("数学", user_id, memory_level="long_term")
        
        print(f"\n=== 压缩结果 ===")
        print(f"短期记忆数量: {len(short_results.results)}")
        print(f"中期记忆数量: {len(medium_results.results)}")
        print(f"长期记忆数量: {len(long_results.results)}")
        
        # 显示中期记忆内容（应该是压缩后的摘要）
        if medium_results.results:
            print(f"\n=== 中期记忆内容（压缩摘要）===")
            for i, result in enumerate(medium_results.results):
                print(f"中期记忆 {i+1}: {result.content}")
                if result.metadata:
                    print(f"  元数据: {result.metadata}")
        
        # 显示短期记忆内容
        if short_results.results:
            print(f"\n=== 剩余短期记忆 ===")
            for i, result in enumerate(short_results.results):
                print(f"短期记忆 {i+1}: {result.content[:50]}...")
        
        print("\n=== 测试完成 ===")
        
        # 验证压缩是否按预期工作
        if len(medium_results.results) > 0 and len(short_results.results) <= memory_config.short_to_medium_count_threshold:
            print("✅ 压缩功能工作正常！记忆已被压缩为摘要。")
        else:
            print("❌ 压缩功能可能存在问题。")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await memory.close()

if __name__ == "__main__":
    asyncio.run(test_fixed_compression())
