#!/usr/bin/env python3
"""
测试memory压缩功能的脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from utils.memory.multi_user_memory import MultiUserMemory, MultiUserMemoryConfig
from autogen_core.memory import MemoryContent, MemoryMimeType
from autogen_ext.models.openai import OpenAIChatCompletionClient
from config import get_config

async def test_memory_compression():
    """测试memory压缩功能"""
    print("开始测试memory压缩功能...")
    
    # 获取配置
    config = get_config()
    
    # 创建LLM客户端
    llm_client = OpenAIChatCompletionClient(
        model=config['agents']['memory_system']['agent_model_name'],
        api_key=config['agents']['memory_system']['agent_model_api_key'],
        model_info={
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": "unknown",
            "structured_output": True,
        }
    )
    
    # 创建记忆系统配置
    memory_config = MultiUserMemoryConfig(
        base_path=os.path.join(os.path.dirname(__file__), "..", "data", "memories"),
        shared_memory_enabled=False,
        user_isolation="db_path",
        collection_name="test_memory_compression",
        
        # 记忆压缩设置
        enable_memory_compression=True,
        llm_client=llm_client,
        
        # 设置较小的阈值以便测试
        short_to_medium_threshold=60,  # 1分钟
        medium_to_long_threshold=120,  # 2分钟
        short_to_medium_count_threshold=5,  # 5条消息
        medium_to_long_count_threshold=10,  # 10条消息
        
        # 查询设置
        short_term_k=10,
        medium_term_k=5,
        long_term_k=3,
        
        # 维护设置
        maintenance_interval=1  # 每次操作后执行维护
    )
    
    # 创建记忆系统
    memory = MultiUserMemory(memory_config)
    
    try:
        user_id = "test_user"
        
        print(f"为用户 {user_id} 添加测试记忆...")
        
        # 添加多条记忆以触发基于数量的压缩
        for i in range(8):
            memory_content = MemoryContent(
                content=f"这是第{i+1}条测试记忆内容，用于测试记忆压缩功能。",
                mime_type=MemoryMimeType.TEXT,
                metadata={
                    "test_id": i+1,
                    "importance": 0.5
                }
            )
            await memory.add(memory_content, user_id)
            print(f"添加了第{i+1}条记忆")
        
        print("\n手动触发记忆压缩...")
        await memory.compress_memories(
            user_id=user_id,
            from_level="short_term",
            to_level="medium_term"
        )
        
        print("\n查询压缩后的记忆...")
        
        # 查询短期记忆
        short_results = await memory.query("测试", user_id, memory_level="short_term")
        print(f"短期记忆数量: {len(short_results.results)}")
        
        # 查询中期记忆
        medium_results = await memory.query("测试", user_id, memory_level="medium_term")
        print(f"中期记忆数量: {len(medium_results.results)}")
        
        # 查询长期记忆
        long_results = await memory.query("测试", user_id, memory_level="long_term")
        print(f"长期记忆数量: {len(long_results.results)}")
        
        print("\n记忆压缩测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await memory.close()

if __name__ == "__main__":
    asyncio.run(test_memory_compression())
