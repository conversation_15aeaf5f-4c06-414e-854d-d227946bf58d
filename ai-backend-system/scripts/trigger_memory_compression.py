#!/usr/bin/env python3
"""
手动触发用户chieh.chu的memory压缩
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from utils.memory.multi_user_memory import MultiUserMemory, MultiUserMemoryConfig
from autogen_ext.models.openai import OpenAIChatCompletionClient
from config import get_config

async def trigger_compression_for_user():
    """手动触发用户chieh.chu的memory压缩"""
    print("开始为用户chieh.chu触发memory压缩...")

    # 获取配置
    config = get_config()

    # 创建LLM客户端
    llm_client = OpenAIChatCompletionClient(
        model=config['agents']['memory_system']['agent_model_name'],
        api_key=config['agents']['memory_system']['agent_model_api_key'],
        model_info={
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": "unknown",
            "structured_output": True,
        }
    )

    # 创建记忆系统配置 - 使用与teaching_assistant_agent相同的配置
    memory_config = MultiUserMemoryConfig(
        base_path=os.path.join(os.path.dirname(__file__), "..", "data", "memories"),
        shared_memory_enabled=False,
        user_isolation="db_path",
        collection_name="memory",

        # 记忆压缩设置
        enable_memory_compression=True,
        llm_client=llm_client,

        # 使用配置文件中的阈值
        short_to_medium_threshold=config['agents']['memory_system']['compression_thresholds']['short_to_medium_time'],
        medium_to_long_threshold=config['agents']['memory_system']['compression_thresholds']['medium_to_long_time'],
        short_to_medium_count_threshold=config['agents']['memory_system']['compression_thresholds']['short_to_medium_count'],
        medium_to_long_count_threshold=config['agents']['memory_system']['compression_thresholds']['medium_to_long_count'],

        # 查询设置
        short_term_k=config['agents']['memory_system']['query_config']['short_term_k'],
        medium_term_k=config['agents']['memory_system']['query_config']['medium_term_k'],
        long_term_k=config['agents']['memory_system']['query_config']['long_term_k'],

        # 维护设置
        maintenance_interval=1  # 强制每次操作后执行维护
    )

    # 创建记忆系统
    memory = MultiUserMemory(memory_config)

    try:
        user_id = "chieh.chu"

        print(f"查询用户 {user_id} 的当前记忆状态...")

        # 查询当前记忆状态
        short_results = await memory.query("", user_id, memory_level="short_term")
        medium_results = await memory.query("", user_id, memory_level="medium_term")
        long_results = await memory.query("", user_id, memory_level="long_term")

        print(f"压缩前 - 短期记忆: {len(short_results.results)}, 中期记忆: {len(medium_results.results)}, 长期记忆: {len(long_results.results)}")

        print("\n手动触发记忆压缩...")
        await memory.compress_memories(
            user_id=user_id,
            from_level="short_term",
            to_level="medium_term"
        )

        print("\n查询压缩后的记忆状态...")

        # 查询压缩后的记忆状态
        short_results_after = await memory.query("", user_id, memory_level="short_term")
        medium_results_after = await memory.query("", user_id, memory_level="medium_term")
        long_results_after = await memory.query("", user_id, memory_level="long_term")

        print(f"压缩后 - 短期记忆: {len(short_results_after.results)}, 中期记忆: {len(medium_results_after.results)}, 长期记忆: {len(long_results_after.results)}")

        print("\n记忆压缩完成！")

    except Exception as e:
        print(f"压缩过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await memory.close()

if __name__ == "__main__":
    asyncio.run(trigger_compression_for_user())
